from flask import Blueprint
from flask_login import current_user
from flask_admin.contrib.sqla import ModelView
from wtforms import <PERSON><PERSON>rea<PERSON>ield
from app import db
from app.utils.admin import SiteSpecificAdminMixin
from app.utils.models import SiteSettings, Site
from common import TrialModelView
from flask_wtf.file import FileField, FileAllowed
from wtforms import Form, validators, ValidationError
from werkzeug.datastructures import FileStorage
import base64


utils_bp = Blueprint(
    "utils_bp", __name__, template_folder="templates", static_folder="static"
)


class SiteAdmin(SiteSpecificAdminMixin, TrialModelView):
    site_id_field = 'id'
    set_site_id = False

    form_columns = ['name', 'enable_otp']

    def is_superuser(self):
        return current_user.is_authenticated and current_user.has_role('superuser')

    @property
    def can_delete(self):
        return self.is_superuser()

    @property
    def can_create(self):
        return self.is_superuser()

    def site_filter(self):
        if self.is_superuser:
            return None
        return self.model.site_id == self.get_current_site_id()

    # def get_query(self):
    #     if self.is_superuser:
    #         return super(ModelView, self).get_query()
    #     return super().get_query().filter(self.site_filter())
    #
    # def get_count_query(self):
    #     return super().get_count_query().filter(self.site_filter())

    def copy_default_site_data(self, new_site_id):
        """Copy data from default site (id=1) to the new site."""
        from app.dashboard.list_models import PrefsFields, PrefsFieldItems, PrefsPredNew, ListLanguage, ListNationality
        from app.dashboard.models import SidebarModule
        from app.utils.models import SiteSettings

        # Copy PrefsFields and their items
        default_prefs_fields = PrefsFields.query.filter_by(site_id=1).all()
        for field in default_prefs_fields:
            # Create new PrefsField
            new_field = PrefsFields(
                fieldname=field.fieldname,
                default_fielditem_id=field.default_fielditem_id,
                site_id=new_site_id
            )
            db.session.add(new_field)
            db.session.flush()  # Flush to get the new field_id

            # Copy associated PrefsFieldItems
            for item in field.fielditems:
                new_item = PrefsFieldItems(
                    field_id=new_field.field_id,
                    fielditem=item.fielditem
                )
                db.session.add(new_item)

        # Copy SidebarModules
        default_modules = SidebarModule.query.filter_by(site_id=1).all()
        for module in default_modules:
            new_module = SidebarModule(
                title=module.title,
                url=module.url,
                icon=module.icon,
                enabled=module.enabled,
                order=module.order,
                site_id=new_site_id
            )
            db.session.add(new_module)

        # Copy SiteSettings
        default_settings = SiteSettings.query.filter_by(site_id=1).all()
        for setting in default_settings:
            new_setting = SiteSettings(
                name=setting.name,
                value=setting.value,
                value_json=setting.value_json,
                site_id=new_site_id
            )
            db.session.add(new_setting)

        # Copy PrefsPredNew
        default_pred_prefs = PrefsPredNew.query.filter_by(site_id=1).all()
        for pred_pref in default_pred_prefs:
            new_pred_pref = PrefsPredNew(
                equationid=pred_pref.equationid,
                age_clipmethod=pred_pref.age_clipmethod,
                age_clipmethodid=pred_pref.age_clipmethodid,
                ht_clipmethod=pred_pref.ht_clipmethod,
                ht_clipmethodid=pred_pref.ht_clipmethodid,
                wt_clipmethod=pred_pref.wt_clipmethod,
                wt_clipmethodid=pred_pref.wt_clipmethodid,
                active=pred_pref.active,
                startdate=pred_pref.startdate,
                enddate=pred_pref.enddate,
                markfordeletion=pred_pref.markfordeletion,
                lastedit=pred_pref.lastedit,
                lasteditby=pred_pref.lasteditby,
                site_id=new_site_id
            )
            db.session.add(new_pred_pref)

        # Copy ListLanguage
        default_languages = ListLanguage.query.filter_by(site_id=1).all()
        for language in default_languages:
            new_language = ListLanguage(
                hl7_code=language.hl7_code,
                hl7_description=language.hl7_description,
                code=language.code,
                description=language.description,
                enabled=language.enabled,
                site_id=new_site_id
            )
            db.session.add(new_language)

        # Copy ListNationality
        default_nationalities = ListNationality.query.filter_by(site_id=1).all()
        for nationality in default_nationalities:
            new_nationality = ListNationality(
                hl7_code=nationality.hl7_code,
                hl7_description=nationality.hl7_description,
                code=nationality.code,
                description=nationality.description,
                site_id=new_site_id
            )
            db.session.add(new_nationality)

        db.session.commit()

    def after_model_change(self, form, model, is_created):
        """Override to copy default site data when a new site is created."""
        res = super(ModelView, self).after_model_change(form, model, is_created)
        if is_created:
            self.copy_default_site_data(model.id)
        return res


class SiteSettingAdmin(SiteSpecificAdminMixin, TrialModelView):
    form_excluded_columns = ['site', 'created', 'updated']
    site_id_field = 'site_id'
    column_list = ['id', 'name', 'value', 'value_json', 'created']

    def _short_json_formatter(view, context, model, name):
        val = getattr(model, name)
        if not val:
            return ''
        return str(val)[:100] + '...' if len(str(val)) > 100 else str(val)

    column_formatters = {
        'value_json': _short_json_formatter,
    }

    # 👇 Here we override the form field for `value` to be a multi-line input
    form_overrides = {
        'value': TextAreaField
    }

    form_extra_fields = {
        'image_upload': FileField(
            'Upload Image',
            validators=[FileAllowed(['jpg', 'jpeg', 'png'], 'Images only!')]
        )
    }

    def is_action_allowed(self, name):
        return super().is_action_allowed(name)

    def on_model_change(self, form, model, is_created):
        image_file: FileStorage = form.image_upload.data
        if image_file:
            image_bytes = image_file.read()
            encoded_string = base64.b64encode(image_bytes).decode('utf-8')
            image_mime = image_file.mimetype

            model.value_json = {
                "image": {
                    "mimetype": image_mime,
                    "data": encoded_string
                }
            }

        super().on_model_change(form, model, is_created)


def add_admins(admin):
    admin.add_view(SiteSettingAdmin(SiteSettings, db.session, category="Misc. Settings"))
    admin.add_view(SiteAdmin(Site, db.session, category='Misc. Settings'))
