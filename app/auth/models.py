from datetime import datetime
import os

from flask_security.models.fsqla_v2 import FsUser<PERSON><PERSON>in, FsRoleMixin
from sqlalchemy import UniqueConstraint, event
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import column_property
from sqlalchemy_utils import Timestamp

from app import db
from audit_mixin import AuditableMixin

roles_users = db.Table(
    'roles_users',
    db.<PERSON>umn('user_id', db.Integer(), db.<PERSON>('ab_user.id')),
    db.<PERSON>n('role_id', db.Integer(), db.<PERSON><PERSON><PERSON>('role.id')),
    extend_existing=True
)

from ..utils.models import Site


class Role(db.Model, FsRoleMixin):
    __tablename__ = "role"

    users = db.relationship(
        "User", secondary='roles_users', back_populates='roles'
    )
    permissions = db.relationship(
        "Permission",
        secondary='role_permissions',
        back_populates="roles"
    )

    def __str__(self):
        return self.name


def get_site_otp_status(context):
    if context is None or type(context).__name__ == 'UserSitesVersion':
        return None
    if hasattr(context, 'get_current_parameters') is False:  # when running in test
        return False
    return db.session.query(Site).get(context.get_current_parameters()['site_id']).enable_otp


class UserSites(db.Model, Timestamp, AuditableMixin):
    __tablename__ = "user_sites"
    __table_args__ = (UniqueConstraint(
        'site_id', 'user_id', name='ui_site_1'
    ),)
    id = db.Column(db.Integer, autoincrement=True, primary_key=True)
    user_id = db.Column(
        'user_id', db.Integer,
        db.ForeignKey('ab_user.id'), nullable=False
    )
    site_id = db.Column('site_id', db.ForeignKey('site.id'), nullable=False)
    user_enable_otp = db.Column(db.Boolean, default=get_site_otp_status)
    # user = db.relationship("User", backref=db.backref(
    #     'site_association'), lazy='subquery')
    # site = db.relationship("Site", backref=db.backref(
    #     'user_association'), lazy='subquery')


class User(db.Model, FsUserMixin, AuditableMixin):
    __tablename__ = "ab_user"
    first_name = db.Column(db.String(512), nullable=False)
    last_name = db.Column(db.String(512), nullable=False)
    full_name = column_property(first_name + " " + last_name)
    selected_lab_id = db.Column(
        db.Integer, db.ForeignKey('labs.id'), nullable=True
    )

    # TOTP related fields
    totp_secret = db.Column(db.String(32), nullable=True)
    totp_enabled = db.Column(db.Boolean, default=False)
    backup_codes = db.Column(db.JSON, nullable=True)  # Store backup codes as a JSON array

    sites = db.relationship(
        'Site', secondary='user_sites', back_populates='users'
    )
    roles = db.relationship(
        'Role', secondary='roles_users', back_populates='users'
    )
    alarms_closed_by_user = db.relationship(
        'Alarm', back_populates='closed_by'
    )
    performed_sessions = db.relationship(
        'Session', back_populates='performed_by'
    )
    direct_permissions = db.relationship(
        "Permission",
        secondary='user_permissions',
        back_populates="users"
    )

    meta = db.Column(db.JSON, nullable=True, default=dict)

    def __str__(self):
        return self.name

    @hybrid_property
    def name(self):
        uname = ('{0} {1}').format(self.first_name or '', self.last_name or '')
        return uname.strip()

    @name.expression
    def name(self):
        return db.func.concat(self.first_name, ' ', self.last_name)

    @staticmethod
    def get_testers(site_id):
        return User.query.filter(User.roles.any(Role.name == 'tester'), User.sites.any(Site.id == site_id))

    @property
    def is_dev_user(self):
        superuser_domains = os.getenv('VITE_APP_SUPERUSER_DOMAINS', '').split(',')
        if not self.email or not superuser_domains:
            return False
        user_domain = '@' + self.email.split('@')[-1]
        return user_domain in superuser_domains

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'active': self.active,
            'roles': [role.name for role in self.roles],
            'sites': [site.name for site in self.sites],
            'password_reset_required': (self.meta or {}).get('password_reset_required', False),
            'totp_enabled': self.totp_enabled,
            'onboarding': (self.meta or {}).get('onboarding', {})
        }


@event.listens_for(User.active, 'set')
def user_active_set(target, value, oldvalue, initiator):
    if value is True:
        target.confirmed_at = datetime.utcnow()


class UserSiteControl(db.Model):
    __tablename__ = "user_site_control"
    __table_args__ = (
        UniqueConstraint('site_id', 'user_id', 'type', 'item_id', name='uq_user_site_control'),
    )

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    site_id = db.Column(db.Integer, db.ForeignKey('site.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('ab_user.id'), nullable=False)
    type = db.Column(db.Enum("device", "parameter", name="control_type"), nullable=False)
    item_id = db.Column(db.String, nullable=False)
    is_favourite = db.Column(db.Boolean, default=False, nullable=False)

    def __repr__(self):
        return (f"<UserSiteControl(site_id={self.site_id}, user_id={self.user_id}, type={self.type}, "
                f"item_id={self.item_id})>")


class Permission(db.Model, AuditableMixin):
    __tablename__ = 'permission'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code = db.Column(db.String(255), unique=True, nullable=False)
    display_text = db.Column(db.String(255), nullable=False)
    help_text = db.Column(db.Text, nullable=True)

    roles = db.relationship("Role", secondary="role_permissions", back_populates="permissions")
    users = db.relationship("User", secondary="user_permissions", back_populates="direct_permissions")

    def __repr__(self):
        return f"<Permission(code='{self.code}')>"

    def __str__(self):
        return self.display_text


class RolePermissions(db.Model, AuditableMixin):
    __tablename__ = 'role_permissions'
    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id', name='uq_role_permission'),
    )
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    role_id = db.Column(db.Integer, db.ForeignKey('role.id'), nullable=False)
    permission_id = db.Column(db.Integer, db.ForeignKey('permission.id'), nullable=False)


class UserPermissions(db.Model, AuditableMixin):
    __tablename__ = 'user_permissions'
    __table_args__ = (
        UniqueConstraint('user_id', 'permission_id', name='uq_user_permission'),
    )
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.ForeignKey('ab_user.id'), nullable=False)
    permission_id = db.Column(db.Integer, db.ForeignKey('permission.id'), nullable=False)
